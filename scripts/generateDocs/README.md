# Nova Documentation Generator

This package provides tools for generating API documentation from TypeScript components in the Nova design system. It automatically extracts props, slots, CSS classes, and documentation from TypeScript source files.

## Architecture

The documentation generator uses a robust parser-based architecture with several key components:

### Core Components

- **ParserFactory**: Coordinates the parsing process and provides a unified API for component documentation
  - Manages parser instances
  - Handles file resolution
  - Coordinates parsing of props, slots, and classes
- **TypeScriptProject**: Manages TypeScript compiler configuration and program creation
  - Sets up compiler options
  - Creates and manages TypeScript Program instances
  - Provides type checking capabilities

### Parsers

- **PropParser**: Extracts prop definitions, types, and JSDoc comments
  - Handles complex TypeScript types
  - Processes function signatures
  - Extracts default values and requirements
  - Supports inheritance from base components
- **SlotParser**: Extracts slot definitions and their props
  - Resolves slot inheritance
  - Handles slot props mapping
  - Supports multiple slot definition patterns
  - Processes default components
- **ClassParser**: Extracts CSS class definitions and documentation
  - Finds class files automatically
  - Processes utility classes
  - Handles class inheritance
  - Supports prefix customization

### Utils

- **docCommentUtils**: Utilities for parsing JSDoc comments
  - Extracts descriptions and tags
  - Processes deprecation information
  - <PERSON>les multi-line comments
  - Supports custom tags
- **typeFormatters**: Utilities for formatting TypeScript types
  - Formats complex types readably
  - Handles union and intersection types
  - Processes function signatures
  - Supports generic types

### Support

- **adapters**: Provides backward compatibility with legacy API
- **componentProcessor**: Handles component discovery and documentation
- **constant**: Defines system-wide constants and mappings
- **createProject**: Manages TypeScript project creation
- **createApiMdx**: Generates Storybook MDX documentation

## Usage

### Command-Line Interface

#### Generate documentation for all components:

```sh
npx ts-node scripts/generateDocs/cli.ts
```

#### Generate documentation for a single component:

```sh
npx ts-node scripts/generateDocs/cli.ts Button
```

#### Generate documentation for a component folder (multiple sub-components):

```sh
# Generate docs for all components in TreeView folder
npx ts-node scripts/generateDocs/cli.ts TreeView

# Generate docs for all components in a specific folder path
npx ts-node scripts/generateDocs/cli.ts ./packages/nova-react-components/TreeView

# Generate docs with custom output directory
npx ts-node scripts/generateDocs/cli.ts TreeView --output ./my-docs
```

#### Generate documentation for a specific file:

```sh
# Process a specific .types.ts file
npx ts-node scripts/generateDocs/cli.ts ./packages/nova-react-components/TreeView/components/TreeItem/TreeItem.types.ts

# With custom output directory
npx ts-node scripts/generateDocs/cli.ts ./path/to/Component.types.ts --output ./custom-docs
```

#### Create Storybook MDX documentation:

```sh
npx ts-node scripts/generateDocs/createApiMdx.ts
```

### CLI Options

| Option | Description | Example |
|--------|-------------|---------|
| `[componentName]` | Generate docs for a single component | `Button` |
| `[folderName]` | Generate docs for all components in a folder | `TreeView` |
| `[filePath]` | Generate docs for a specific file | `./packages/.../Button.types.ts` |
| `--output [dir]` | Custom output directory | `--output ./my-docs` |

### Component Folder Support

The generator now supports complex component structures with multiple sub-components:

**Example: TreeView Structure**
```
TreeView/
├── components/
│   ├── TreeItem/
│   │   ├── TreeItem.types.ts     ← Will be processed
│   │   ├── TreeItem.tsx
│   │   └── ...
│   └── TreeView/
│       ├── TreeView.types.ts     ← Will be processed
│       ├── TreeView.tsx
│       └── ...
└── index.ts
```

**Generated Output:**
```
docs/api/TreeView/
├── TreeView.api.md              # Individual TreeView docs
├── TreeItem.api.md              # Individual TreeItem docs
└── TreeView.combined.md         # Combined docs for both components
```

### API Usage

```typescript
import { ParserFactory } from './scripts/generateDocs/core/ParserFactory';
import { PropParser, SlotParser, ClassParser } from './scripts/generateDocs/parsers';

// Using ParserFactory (recommended)
const factory = new ParserFactory();
const componentDocs = factory.parseComponent('/path/to/component.types.ts', 'ComponentName');

// Using individual parsers
const propParser = new PropParser('/path/to/component.types.ts');
const props = propParser.parseProps('ComponentName');

const slotParser = new SlotParser('/path/to/component.types.ts');
const slots = slotParser.parseSlots('ComponentName');

const classParser = new ClassParser('/path/to/component.types.ts');
const classes = classParser.parseClasses('ComponentName');
```

## Documentation Format

### Output Structure

The generator produces three types of documentation:

1. **Individual API Files** (`.api.md`)
   - Component description
   - Import information
   - Props documentation
   - Slots documentation
   - CSS classes documentation

2. **Combined API Documentation** (`.combined.md`)
   - Table of contents
   - All component APIs in one file
   - Cross-component references
   - Unified navigation

3. **Storybook MDX Files** (`.mdx`)
   - Storybook integration
   - Interactive documentation
   - Component previews

### JSDoc Comment Format

#### Component Documentation

```typescript
/**
 * A description of the component.
 * 
 * @since 1.0.0
 * @deprecated Use NewComponent instead
 * @example
 * ```jsx
 * <ComponentName prop="value" />
 * ```
 */
```

#### Props Documentation

```typescript
/**
 * A description of the prop.
 * 
 * @default "default value"
 * @required
 * @deprecated Use newProp instead
 * @param {string} paramName - Description of the parameter
 * @returns {boolean} Description of the return value
 * @example
 * ```jsx
 * <Component propName="example" />
 * ```
 */
```

#### Slots Documentation

```typescript
/**
 * A description of the slot.
 * 
 * @default 'div'
 * @deprecated Use newSlot instead
 * @example
 * ```jsx
 * <Component>
 *   <div slot="slotName">Content</div>
 * </Component>
 * ```
 */
```

#### CSS Classes Documentation

```typescript
/**
 * A description of the CSS class.
 * 
 * @type css|state|modifier
 * @prefix component
 * @deprecated Use newClass instead
 * @example
 * ```css
 * .NovaComponent-root { /* styles */ }
 * ```
 */
```
