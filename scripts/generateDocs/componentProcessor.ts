/* eslint-disable @typescript-eslint/no-explicit-any */
import path from 'path';
import * as fs from 'fs';
import {
  COMPONENT_FOLDER_MAPPING,
  IGNORED_FOLDERS,
  IGNORED_PATTERNS,
  COMPONENT_NAME_MAPPING,
  GLOBAL_STATE_CLASSES,
  IGNORED_REQUIRED_PROPS,
} from './constant';
import { ParsedComponent } from './types';
import { ParserFactory } from './core/ParserFactory';

export class ComponentProcessor {
  private componentsDir: string;
  private storybookDir: string;
  private parserFactory: ParserFactory;

  constructor(componentsDir: string, storybookDir: string) {
    this.componentsDir = componentsDir;
    this.storybookDir = storybookDir;
    const tsConfigPath = path.resolve(process.cwd(), 'tsconfig.json');
    this.parserFactory = new ParserFactory(tsConfigPath);
  }

  /**
   * Find component folders that contain multiple sub-components
   */
  public findComponentFolders(): Array<{ folderName: string; folderPath: string; components: string[] }> {
    console.log(`Scanning for component folders in: ${this.componentsDir}`);
    const componentFolders: Array<{ folderName: string; folderPath: string; components: string[] }> = [];

    const scanDirectory = (dir: string, depth: number = 0) => {
      // Don't scan too deep to avoid performance issues
      if (depth > 3) return;

      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory() && !IGNORED_FOLDERS.includes(entry.name)) {
            // Check if this directory contains .types.ts files
            const typeFiles = this.findTypesFilesInDirectory(fullPath);

            if (typeFiles.length > 0) {
              const components = typeFiles.map((file) => file.componentName);
              componentFolders.push({
                folderName: entry.name,
                folderPath: fullPath,
                components,
              });
              console.log(`Found component folder: ${entry.name} with components: ${components.join(', ')}`);
            } else {
              // Continue scanning subdirectories
              scanDirectory(fullPath, depth + 1);
            }
          }
        }
      } catch (error) {
        console.error(`Error scanning directory ${dir}:`, error);
      }
    };

    scanDirectory(this.componentsDir);
    return componentFolders;
  }

  /**
   * Find all .types.ts files in a specific directory
   */
  private findTypesFilesInDirectory(dirPath: string): Array<{ componentName: string; filePath: string }> {
    const results: Array<{ componentName: string; filePath: string }> = [];

    function scanDirectory(currentPath: string) {
      try {
        const entries = fs.readdirSync(currentPath, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name);

          if (entry.isDirectory()) {
            // Skip node_modules, tests, hooks, and other non-component directories
            if (!['node_modules', '__tests__', 'test', 'tests', '.git', 'hooks'].includes(entry.name)) {
              scanDirectory(fullPath);
            }
          } else if (entry.name.endsWith('.types.ts')) {
            const componentName = entry.name.replace('.types.ts', '');
            results.push({
              componentName,
              filePath: fullPath,
            });
          }
        }
      } catch (error) {
        console.error(`Error scanning directory ${currentPath}:`, error);
      }
    }

    scanDirectory(dirPath);
    return results;
  }

  public findComponents(): string[] {
    console.log(`Scanning for components in: ${this.componentsDir}`);
    const components = new Set<string>();

    const scanDirectory = (dir: string) => {
      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory()) {
            if (IGNORED_FOLDERS.includes(entry.name)) {
              console.log(`Skipping ignored folder: ${entry.name}`);
              continue;
            }

            const dirContents = fs.readdirSync(fullPath);
            dirContents
              .filter((file) => file.endsWith('.types.ts'))
              .filter((file) => !IGNORED_PATTERNS.some((pattern) => pattern.test(file)))
              .forEach((file) => {
                const componentName = file.replace('.types.ts', '');
                console.log(`Found component: ${componentName} in ${fullPath}`);
                components.add(componentName);
              });

            scanDirectory(fullPath);
          }
        }
      } catch (error) {
        console.error(`Error scanning directory ${dir}:`, error);
      }
    };

    scanDirectory(this.componentsDir);
    return Array.from(components).sort();
  }

  /**
   * Process a component folder containing multiple sub-components
   */
  public processComponentFolder(folderPath: string, customOutputDir?: string): void {
    try {
      console.log(`\nProcessing component folder: ${folderPath}...`);

      const typeFiles = this.findTypesFilesInDirectory(folderPath);

      if (typeFiles.length === 0) {
        console.warn(`No .types.ts files found in folder: ${folderPath}`);
        return;
      }

      console.log(`Found ${typeFiles.length} component type files:`);
      typeFiles.forEach((file) => console.log(`  - ${file.componentName} (${file.filePath})`));

      const folderName = path.basename(folderPath);
      const generatedDocs: Array<{ componentName: string; filePath: string; content: string }> = [];

      // Determine the primary output directory
      let defaultOutputDir: string;
      let useStorybookFolders = false;

      if (customOutputDir) {
        defaultOutputDir = path.resolve(process.cwd(), customOutputDir);
      } else {
        // Try to find a storybook folder for the main component (folder name)
        const storyFolder = this.findStoryFolder(folderName);
        if (storyFolder) {
          defaultOutputDir = path.join(this.storybookDir, 'src/stories', storyFolder);
          useStorybookFolders = true;
          console.log(`Found storybook folder '${storyFolder}' for ${folderName}`);
        } else {
          // Fall back to docs/api/{folderName}
          defaultOutputDir = path.join(process.cwd(), 'docs/api', folderName);
          console.log(`No storybook folder found for ${folderName}, using docs/api/${folderName}`);
        }
      }

      // Process each component
      for (const typeFile of typeFiles) {
        try {
          console.log(`\nProcessing ${typeFile.componentName}...`);

          const parsedComponent = this.parserFactory.parseComponent(typeFile.filePath, typeFile.componentName);
          const content = this.generateMarkdown(typeFile.componentName, parsedComponent);

          // Determine output path for each component
          let outputDir: string;
          let outputPath: string;

          if (customOutputDir) {
            // Use custom output directory for all components
            outputDir = defaultOutputDir;
            outputPath = path.join(outputDir, `${typeFile.componentName}.api.md`);
          } else if (useStorybookFolders) {
            // Try to find specific storybook folder for each component
            const componentStoryFolder = this.findStoryFolder(typeFile.componentName);
            if (componentStoryFolder) {
              outputDir = path.join(this.storybookDir, 'src/stories', componentStoryFolder);
              outputPath = path.join(outputDir, `${typeFile.componentName}.api.md`);
              console.log(`  → Placing in storybook folder: ${componentStoryFolder}`);
            } else {
              // Use the main folder's storybook location
              outputDir = defaultOutputDir;
              outputPath = path.join(outputDir, `${typeFile.componentName}.api.md`);
              console.log(`  → Placing in main storybook folder: ${path.basename(defaultOutputDir)}`);
            }
          } else {
            // Use docs/api location
            outputDir = defaultOutputDir;
            outputPath = path.join(outputDir, `${typeFile.componentName}.api.md`);
          }

          // Ensure output directory exists
          fs.mkdirSync(outputDir, { recursive: true });
          fs.writeFileSync(outputPath, content, 'utf-8');

          generatedDocs.push({
            componentName: typeFile.componentName,
            filePath: outputPath,
            content,
          });

          console.log(`✓ Generated API documentation for ${typeFile.componentName}`);
          console.log(`  Output: ${outputPath}`);
        } catch (error) {
          console.error(`Error processing ${typeFile.componentName}:`, error);
        }
      }

      // Generate a combined index file in the default directory
      fs.mkdirSync(defaultOutputDir, { recursive: true });
    } catch (error) {
      console.error(`Error processing component folder ${folderPath}:`, error);
    }
  }

  public findComponentFiles(componentName: string): { typeFile: string; classFile?: string } | null {
    let typeFile: string | null = null;

    const findFile = (dir: string): boolean => {
      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          if (entry.isDirectory() && !IGNORED_FOLDERS.includes(entry.name)) {
            if (findFile(fullPath)) return true;
          } else if (entry.name === `${componentName}.types.ts`) {
            typeFile = fullPath;
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error(`Error searching for files in ${dir}:`, error);
        return false;
      }
    };

    findFile(this.componentsDir);

    if (!typeFile) {
      return null;
    }

    const componentDir = path.dirname(typeFile);
    const classFile = path.join(componentDir, `${componentName}.classes.ts`);

    return {
      typeFile,
      classFile: fs.existsSync(classFile) ? classFile : undefined,
    };
  }

  private findStoryFolder(componentName: string): string | null {
    // First check if there's a direct mapping
    if (COMPONENT_FOLDER_MAPPING[componentName]) {
      const mappedFolder = COMPONENT_FOLDER_MAPPING[componentName];
      const mappedPath = path.join(this.storybookDir, 'src/stories', mappedFolder);
      if (fs.existsSync(mappedPath)) {
        return mappedFolder;
      }
    }

    const storiesDir = path.join(this.storybookDir, 'src/stories');
    if (!fs.existsSync(storiesDir)) return null;

    const storyDirs = fs
      .readdirSync(storiesDir, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name);

    // Exact match
    if (storyDirs.includes(componentName)) {
      return componentName;
    }

    // Base component match (e.g., "List" for "ListItem")
    const baseComponentName = componentName.match(/^[A-Z][a-z]+/)?.[0];
    if (baseComponentName && storyDirs.includes(baseComponentName)) {
      return baseComponentName;
    }

    // Prefix match
    return storyDirs.find((dir) => componentName.startsWith(dir)) || null;
  }

  public processComponent(componentName: string): void {
    try {
      console.log(`\nProcessing ${componentName}...`);

      // Find component files
      const files = this.findComponentFiles(componentName);
      if (!files) {
        console.warn(`No files found for ${componentName}`);
        if (process.argv[2] === componentName) {
          console.log(`Attempting to find ${componentName} in all possible locations...`);
        }
        return;
      }

      console.log(`Types file: ${files.typeFile}`);
      if (files.classFile) {
        console.log(`Classes file: ${files.classFile}`);
      }

      // Parse component using the existing parserFactory instance
      const parsedComponent = this.parserFactory.parseComponent(files.typeFile, componentName);

      // Find story folder
      const storyFolder = this.findStoryFolder(componentName);
      let outputDir: string;
      let outputPath: string;

      if (!storyFolder) {
        // Fallback to docs/api folder
        outputDir = path.join(process.cwd(), 'docs/api');
        // Create docs/api directory if it doesn't exist
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }
        outputPath = path.join(outputDir, `${componentName}.api.md`);
        console.log(`No matching story folder found for ${componentName}, saving to docs/api folder instead.`);
      } else {
        outputDir = path.join(this.storybookDir, 'src/stories', storyFolder);
        if (!fs.existsSync(outputDir)) {
          console.warn(`Story folder not found: ${outputDir}, falling back to docs/api folder`);
          outputDir = path.join(process.cwd(), 'docs/api');
          if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
          }
        }
        outputPath = path.join(outputDir, `${componentName}.api.md`);
      }

      // Generate and write documentation
      const content = this.generateMarkdown(componentName, parsedComponent);

      fs.mkdirSync(outputDir, { recursive: true });
      fs.writeFileSync(outputPath, content, 'utf-8');

      console.log(`✓ Generated API documentation for ${componentName}`);
      console.log(`  Output: ${outputPath}`);
    } catch (error) {
      console.error(`Error processing ${componentName}:`, error);
    }
  }

  private generateMarkdown(componentName: string, component: ParsedComponent): string {
    const escapeTableCell = (text: string): string => {
      // Escape pipes and backticks in table cells
      return text.replace(/\|/g, '⏐').replace(/`/g, '\\`');
    };

    const formatDescription = (text: string): string => {
      if (!text) return '';

      // Replace newlines with <br> for table cells
      return text
        .split('\n')
        .map((line) => line.trim())
        .filter(Boolean) // Remove empty lines
        .join('<br>');
    };

    const formatType = (type: string): string => {
      // Handle types with special characters
      if (type.includes('|') || type.includes('<') || type.includes('>')) {
        return `\`${escapeTableCell(type)}\``;
      }
      return type;
    };

    // Update the title to use the mapping if available
    const mappedComponentName = COMPONENT_NAME_MAPPING[componentName];
    const displayName = mappedComponentName || componentName;
    let content = `# ${displayName} API\n\n`;
    content += `API reference docs for the React ${displayName} component. Learn about the props, CSS, and other APIs of this exported module.\n\n`;

    // Fixed import section with proper formatting (no leading spaces)
    content += `## Import\n\n`;
    content += `To use the \`${displayName}\` component, you can choose to import it directly or through the main entry point.\n\n`;
    if (mappedComponentName) {
      const rootComponentName = mappedComponentName.split('.')[0];
      content += `\`\`\`jsx\nimport { ${rootComponentName} } from '@hxnova/react-components/${rootComponentName}';\n// or\nimport { ${rootComponentName} } from '@hxnova/react-components';\n\n// usage\n<${mappedComponentName}>{children}</${mappedComponentName}>\n\`\`\`\n\n`;
    } else {
      content += `\`\`\`jsx\nimport { ${componentName} } from '@hxnova/react-components/${componentName}';\n// or\nimport { ${componentName} } from '@hxnova/react-components';\n\`\`\`\n\n`;
    }

    if (component.description) {
      content += `${component.description.replace(/\n/g, '\n')}\n\n`;
    }

    // Props table
    if (component.props.length > 0) {
      content += `## Props\n\n`;
      content += `The properties available for the \`${displayName}\` component. Props of the native component are also available.\n\n`;
      content += `| Name | Type | Default | Description |\n`;
      content += `| ---- | ---- | ------- | ----------- |\n`;
      content += component.props
        .map((prop) => {
          const name = prop.required && !IGNORED_REQUIRED_PROPS.includes(prop.name) ? `${prop.name}*` : prop.name;
          let description = formatDescription(prop.description);

          if (prop.signature) {
            description += `  \n\n**Signature:**  \n\`${escapeTableCell(prop.signature)}\``;
          }

          if (prop.signatureArgs?.length) {
            description += '  \n\n**Parameters:**  \n';
            prop.signatureArgs.forEach((arg) => {
              description += `- \`${arg.name}\`: ${formatDescription(arg.description)}  \n`;
            });
          }

          if (prop.signatureReturn) {
            description += `  \n**Returns:** ${formatDescription(prop.signatureReturn.description)}`;
          }

          if (prop.isDeprecated) {
            description = `⚠️ **Deprecated**${prop.deprecationInfo ? ': ' + prop.deprecationInfo : ''}<br><br>${description}`;
          }

          const defaultValue = prop.defaultValue ? escapeTableCell(prop.defaultValue) : '-';
          const type = formatType(prop.type);

          return `| **${name}** | \`${type}\` | ${defaultValue === '-' ? '-' : `\`${defaultValue}\``} | ${description} |`;
        })
        .join('\n');
      content += '\n\n';
    }

    // Slots table - Updated to match MUI format
    if (component.slots.length > 0) {
      content += `## Slots\n\n`;
      content += `Slots allow for more granular control over the rendering of specific parts of the \`${displayName}\` component.\n\n`;
      content += `| Slot name | Class name | Default component | Description |\n`;
      content += `| --------- | ---------- | ----------------- | ----------- |\n`;
      content += component.slots
        .map((slot) => {
          const className = `.Nova${componentName}-${slot.name}`;
          const defaultValue = `${slot.defaultValue}` || `'div'`;
          return `| ${slot.name} | ${className} | \`${formatType(defaultValue)}\` | ${formatDescription(slot.description)} |`;
        })
        .join('\n');
      content += '\n\n';
    }

    // Filter out slot-related classes from the Classes section
    // This is the key update - we'll skip any classes that correspond to slot names
    const slotNames = component.slots.map((slot) => slot.name);
    const nonSlotClasses = component.classes.filter((cls) => !slotNames.includes(cls.name));

    // Classes table - Only display non-slot classes
    if (nonSlotClasses.length > 0) {
      content += `## CSS classes\n\n`;
      content += `CSS classes for different states and variations of the \`${displayName}\` component.\n\n`;
      content += `| Class name | Rule name | Description |\n`;
      content += `| ---------- | --------- | ----------- |\n`;
      content += nonSlotClasses
        .map((cls) => {
          const name = `.Nova${GLOBAL_STATE_CLASSES[cls.name] ? '' : componentName}-${cls.name}`;
          return `| ${name} | \`${cls.name}\` | ${formatDescription(cls.description)} |`;
        })
        .join('\n');
      content += '\n\n';
    }

    return content;
  }

  /**
   * Process a single file by direct path
   */
  public processSingleFile(filePath: string, customOutputDir?: string): void {
    try {
      // Resolve the file path
      const resolvedPath = path.isAbsolute(filePath) ? filePath : path.resolve(process.cwd(), filePath);

      if (!fs.existsSync(resolvedPath)) {
        console.error(`File not found: ${resolvedPath}`);
        return;
      }

      // Extract component name from file path
      const fileName = path.basename(resolvedPath);
      let componentName: string;

      if (fileName.endsWith('.types.ts')) {
        componentName = fileName.replace('.types.ts', '');
      } else if (fileName.endsWith('.tsx')) {
        componentName = fileName.replace('.tsx', '');
      } else if (fileName.endsWith('.ts')) {
        componentName = fileName.replace('.ts', '');
      } else {
        console.error(`Unsupported file type: ${fileName}. Expected .types.ts, .tsx, or .ts`);
        return;
      }

      console.log(`Processing component: ${componentName} from ${resolvedPath}`);

      // Use the existing parserFactory instance
      const parsedComponent = this.parserFactory.parseComponent(resolvedPath, componentName);

      // Generate markdown documentation
      const content = this.generateMarkdown(componentName, parsedComponent);

      // Determine output path
      let outputPath: string;
      if (customOutputDir) {
        const outputDir = path.resolve(process.cwd(), customOutputDir);
        fs.mkdirSync(outputDir, { recursive: true });
        outputPath = path.join(outputDir, `${componentName}.api.md`);
      } else {
        // Use same directory as the component file
        const outputDir = path.dirname(resolvedPath);
        outputPath = path.join(outputDir, `${componentName}.api.md`);
      }

      // Write the documentation
      fs.writeFileSync(outputPath, content, 'utf-8');

      console.log(`✓ Generated API documentation for ${componentName}`);
      console.log(`  Input: ${resolvedPath}`);
      console.log(`  Output: ${outputPath}`);
    } catch (error) {
      console.error(`Error processing file ${filePath}:`, error);
    }
  }
}
