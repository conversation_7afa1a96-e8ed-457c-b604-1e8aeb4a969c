/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import * as path from 'path';
import * as fs from 'fs';
import { ComponentProcessor } from './componentProcessor';

/**
 * Command-line interface for generating API documentation
 * This script processes components and generates documentation files
 *
 * Usage:
 *   ts-node cli.ts [componentName|folderPath] [--output outputDir]
 *
 * Examples:
 *   ts-node cli.ts Button                                    # Generate docs for Button component
 *   ts-node cli.ts TreeView                                  # Generate docs for all components in TreeView folder
 *   ts-node cli.ts ./packages/nova-react-components/TreeView # Direct folder path
 *   ts-node cli.ts Button --output ./custom-docs            # Custom output directory
 *
 * If componentName/folderPath is provided, only that component/folder will be processed.
 * Otherwise, all components will be processed.
 */
async function main() {
  try {
    const componentsDir = path.resolve(__dirname, '../../packages/nova-react-components');
    const storybookDir = path.resolve(__dirname, '../../apps/storybook');

    console.log('Starting API documentation generation...');
    console.log(`Components directory: ${componentsDir}`);
    console.log(`Storybook directory: ${storybookDir}\n`);

    const processor = new ComponentProcessor(componentsDir, storybookDir);

    // Parse command line arguments
    const args = process.argv.slice(2);
    const targetComponentOrPath = args[0];
    const outputDirIndex = args.indexOf('--output');
    const customOutputDir = outputDirIndex !== -1 ? args[outputDirIndex + 1] : undefined;

    if (targetComponentOrPath) {
      console.log(`Generating documentation for: ${targetComponentOrPath}`);

      // Check if it's a direct file path
      if (targetComponentOrPath.includes('/') || targetComponentOrPath.includes('\\')) {
        const resolvedPath = path.isAbsolute(targetComponentOrPath)
          ? targetComponentOrPath
          : path.resolve(process.cwd(), targetComponentOrPath);

        if (fs.existsSync(resolvedPath) && fs.statSync(resolvedPath).isDirectory()) {
          // It's a directory path
          processor.processComponentFolder(resolvedPath, customOutputDir);
        } else if (fs.existsSync(resolvedPath) && resolvedPath.endsWith('.ts')) {
          // It's a file path
          processor.processSingleFile(resolvedPath, customOutputDir);
        } else {
          console.error(`Path not found or invalid: ${resolvedPath}`);
        }
      } else {
        // It could be a component name or folder name
        const componentFolderPath = path.join(componentsDir, targetComponentOrPath);

        if (fs.existsSync(componentFolderPath) && fs.statSync(componentFolderPath).isDirectory()) {
          // It's a component folder (like TreeView)
          processor.processComponentFolder(componentFolderPath, customOutputDir);
        } else {
          // Try as a single component
          processor.processComponent(targetComponentOrPath);
        }
      }
    } else {
      // Process all components
      const components = processor.findComponents();
      console.log(`\nFound ${components.length} components to process:`);
      console.log(components.join(', '), '\n');

      for (const componentName of components) {
        processor.processComponent(componentName);
      }
    }

    console.log('\nDocumentation generation complete!');
  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  }
}

main();
