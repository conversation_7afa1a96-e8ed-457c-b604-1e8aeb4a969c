import { Canvas, Meta } from '@storybook/blocks';
import CodeExpand from '../../components/codeExpand/CodeExpand';
import ResponsiveBreakpointsExample from './Examples/ResponsiveBreakpointsExample';
import ResponsiveBreakpointsExampleSource from './Examples/ResponsiveBreakpointsExample.tsx?raw';

<Meta title="@hxnova/react-components/Utils/useMediaQuery/Examples" />

## Responsive Breakpoints

The useMediaQuery hook provides a way to conditionally apply styles or render different content based on media queries. This example demonstrates how to use the hook with Nova's breakpoints to create responsive components.

**Important:** With PigmentCSS, the traditional MUI pattern of `useTheme().breakpoints.up('sm')` doesn't work because `useTheme()` only provides static CSS variables. Nova's `useMediaQuery` hook solves this by providing direct access to <PERSON>'s breakpoint system.

This demo shows:
- Current device type based on screen size
- Status of different breakpoint queries
- Conditional rendering based on screen size
- Custom media queries for orientation and accessibility preferences

Try resizing your browser window to see the breakpoints in action!

<div className="sb-unstyled">
  <ResponsiveBreakpointsExample />
</div>
<CodeExpand code={ ResponsiveBreakpointsExampleSource } showBorderTop style={{marginTop: 16}}/>

## Quick Reference

### Basic Usage

```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
```

### Available Breakpoints

Nova uses the following breakpoints:

- `xs`: 0px (Mobile)
- `sm`: 600px (Tablet Portrait)
- `md`: 840px (Tablet Landscape)
- `lg`: 1200px (Laptop Display)
- `xl`: 1600px (Desktop Display)

### Breakpoint Methods

```tsx
// Screen size queries
const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
const isTablet = useMediaQuery(NovaTheme.breakpoints.between('sm', 'md'));
const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));
const isExactlyMd = useMediaQuery(NovaTheme.breakpoints.only('md'));
```

### Custom Media Queries

```tsx
// Accessibility preferences
const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');
const prefersHighContrast = useMediaQuery('(prefers-contrast: high)');
const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');

// Device capabilities
const isLandscape = useMediaQuery('(orientation: landscape)');
const hasHover = useMediaQuery('(hover: hover)');
const isHighDensity = useMediaQuery('(min-resolution: 2dppx)');
```

### SSR-Safe Pattern

```tsx
// Recommended for SSR applications
const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'), {
  defaultMatches: false, // Assume desktop by default
});
```

### Common Responsive Patterns

```tsx
import { useMediaQuery } from '@hxnova/react-components';
import { NovaTheme } from '@hxnova/themes';

function ResponsiveComponent() {
  const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(NovaTheme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));

  return (
    <div>
      {isMobile && <MobileLayout />}
      {isTablet && <TabletLayout />}
      {isDesktop && <DesktopLayout />}
    </div>
  );
}
```

### Performance-Optimized Hook

```tsx
function useResponsiveBreakpoints() {
  const isMobile = useMediaQuery(NovaTheme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(NovaTheme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(NovaTheme.breakpoints.up('lg'));

  return useMemo(() => ({
    isMobile, isTablet, isDesktop,
    deviceType: isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop',
    columnsCount: isMobile ? 1 : isTablet ? 2 : 3,
  }), [isMobile, isTablet, isDesktop]);
}
```
